# 🎨 Design System Documentation

## Architecture Overview

Our design system follows a **three-layer architecture** that ensures consistency, maintainability, and scalability:

```
┌─────────────────────────────────────────────────────────────┐
│                    APPLICATION LAYER                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Components    │ │   Components    │ │   Components    ││
│  │   (Header)      │ │   (Sidebar)     │ │   (Cards)       ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   SEMANTIC LAYER                            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │ Semantic Utils  │ │ Component       │ │ Layout          ││
│  │ (.bg-card)      │ │ Classes         │ │ Classes         ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     TOKEN LAYER                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │ Figma Colors    │ │ Angular M3      │ │ CSS Custom      ││
│  │ (Source)        │ │ Theme           │ │ Properties      ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## Color System

### 🎯 **Design Tokens (Source of Truth)**

All colors come from **Figma design tokens** and are defined in `src/styles/_color.scss`:

#### Light Mode Colors

```scss
--md-sys-color-primary: #296197 // Brand blue
  --md-sys-color-surface: #f7f9ff // Page background
  --md-sys-color-on-surface: #181c20 // Primary text
  --md-sys-color-surface-container: #eceef4; // Card background
```

#### Dark Mode Colors

```scss
--md-sys-color-primary: #a1cafd // Brand blue (dark)
  --md-sys-color-surface: #101418 // Page background (dark)
  --md-sys-color-on-surface: #e0e2e8 // Primary text (dark)
  --md-sys-color-surface-container: #1c2024; // Card background (dark)
```

## Semantic Utility Classes

### 🏗️ **Background Classes**

| Class                  | Purpose               | Light Mode | Dark Mode | Use Case              |
| ---------------------- | --------------------- | ---------- | --------- | --------------------- |
| `.bg-page`             | Main page background  | `#f7f9ff`  | `#101418` | Body, main containers |
| `.bg-card`             | Card/panel background | `#eceef4`  | `#1c2024` | Cards, modals, panels |
| `.bg-elevated`         | Elevated surfaces     | `#e6e8ee`  | `#272a2f` | Dropdowns, tooltips   |
| `.bg-button-primary`   | Primary buttons       | `#296197`  | `#a1cafd` | CTA buttons           |
| `.bg-button-secondary` | Secondary buttons     | `#e0e2e8`  | `#323539` | Secondary actions     |

### 📝 **Text Classes**

| Class              | Purpose               | Light Mode | Dark Mode | Use Case               |
| ------------------ | --------------------- | ---------- | --------- | ---------------------- |
| `.text-primary`    | Primary text          | `#181c20`  | `#e0e2e8` | Headings, main content |
| `.text-secondary`  | Secondary text        | `#43474e`  | `#c3c6cf` | Descriptions, labels   |
| `.text-accent`     | Accent/brand text     | `#296197`  | `#a1cafd` | Links, highlights      |
| `.text-on-primary` | Text on primary bg    | `#ffffff`  | `#003259` | Button text            |
| `.text-on-dark`    | Text on dark surfaces | `#001d36`  | `#d2e4ff` | Dark button text       |

### 🎨 **Icon Classes**

| Class              | Purpose             | Light Mode | Dark Mode | Use Case               |
| ------------------ | ------------------- | ---------- | --------- | ---------------------- |
| `.icon-accent`     | Brand/accent icons  | `#296197`  | `#a1cafd` | Primary actions, logos |
| `.icon-primary`    | Standard icons      | `#181c20`  | `#e0e2e8` | Navigation, general UI |
| `.icon-secondary`  | Muted icons         | `#43474e`  | `#c3c6cf` | Secondary actions      |
| `.icon-on-primary` | Icons on primary bg | `#ffffff`  | `#003259` | Button icons           |

### 🔲 **Border Classes**

| Class            | Purpose          | Light Mode | Dark Mode | Use Case               |
| ---------------- | ---------------- | ---------- | --------- | ---------------------- |
| `.border-subtle` | Subtle borders   | `#c3c6cf`  | `#43474e` | Card borders, dividers |
| `.border-normal` | Standard borders | `#73777f`  | `#8d9199` | Input fields, emphasis |

## Component Guidelines

### 🎯 **Header Components**

```html
<!-- Theme Toggle & Notification Buttons -->
<button class="header-icon-button">
  <mat-icon class="icon-accent">notifications</mat-icon>
</button>
```

**Styling:**

- Light: White background (`#ffffff`) with blue icons
- Dark: Dark background (`#36393e`) with blue icons
- No borders, clean circular design

### 🎯 **Card Components**

```html
<!-- Standard Card -->
<div class="bg-card border border-subtle">
  <h3 class="text-primary">Card Title</h3>
  <p class="text-secondary">Card description</p>
</div>
```

### 🎯 **Button Components**

```html
<!-- Primary Button -->
<button class="bg-button-primary text-on-primary">Primary Action</button>

<!-- Secondary Button -->
<button class="bg-button-secondary text-primary">Secondary Action</button>
```

## Usage Examples

### ✅ **Good Examples**

```html
<!-- Clear semantic meaning -->
<div class="bg-card text-primary border-subtle">
  <button class="bg-button-primary text-on-primary">
    <mat-icon class="icon-accent">star</mat-icon>
  </button>
</div>
```

### ❌ **Avoid**

```html
<!-- Unclear technical names -->
<div class="u-bg-surface-container u-text-on-surface">
  <button style="background: var(--md-sys-color-primary)"></button>
</div>
```

## File Organization

```
src/
├── styles/
│   ├── _color.scss          # Design tokens (Figma colors)
│   ├── _utilities.scss      # Semantic utility classes
│   └── _components.scss     # Global component styles
├── app/
│   └── shared/
│       └── components/
│           ├── header/
│           │   ├── header.component.html
│           │   └── header.component.css  # Component-specific styles
│           └── sidebar/
│               ├── sidebar.component.html
│               └── sidebar.component.scss
└── m3-theme.scss           # Angular Material theme (aligned with Figma)
```

## Best Practices

### 🎯 **Do's**

- ✅ Use semantic class names (`.bg-card`, `.text-primary`)
- ✅ Keep component-specific styles in component files
- ✅ Use CSS custom properties for consistency
- ✅ Follow the three-layer architecture

### ❌ **Don'ts**

- ❌ Use inline styles for theming
- ❌ Mix utility classes with component-specific styles
- ❌ Use technical Material Design names in templates
- ❌ Hardcode colors outside the token system

## Migration Guide

When updating existing components:

1. **Replace hardcoded colors** with semantic classes
2. **Move component styles** to component-specific files
3. **Use semantic naming** instead of technical terms
4. **Test in both themes** to ensure proper contrast

## Implementation Status

### ✅ **Completed**

- [x] Figma color tokens defined in `_color.scss`
- [x] Semantic utility classes created
- [x] Header and sidebar components updated
- [x] Angular Material theme override implemented
- [x] Design system documentation created

### 🔄 **In Progress**

- [ ] Remaining components (cards, forms, modals)
- [ ] Typography and spacing tokens
- [ ] Component-specific documentation

### 📋 **Next Steps**

1. **Test color consistency** across all Material components
2. **Update remaining components** to use semantic classes
3. **Add spacing and typography** to the design system
4. **Create component library** documentation

## Color System Alignment

The system now ensures **100% consistency** between:

- ✅ Figma design tokens (`_color.scss`)
- ✅ Angular Material components (override in `styles.scss`)
- ✅ Custom components (semantic utilities)
- ✅ Light and dark mode switching

## Quick Reference

### Most Common Classes

```scss
// Backgrounds
.bg-page          // Main page background
.bg-card          // Card/panel background
.bg-button-primary // Primary button background

// Text
.text-primary     // Main text color
.text-secondary   // Muted text color
.text-accent      // Brand/link color

// Icons
.icon-accent      // Brand color icons
.icon-primary     // Standard icons
.icon-secondary   // Muted icons

// Borders
.border-subtle    // Light borders
.border-normal    // Standard borders
```
