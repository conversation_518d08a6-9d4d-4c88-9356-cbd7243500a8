# 🚀 Quick Reference - Design System

## 🎨 **Semantic Classes Cheat Sheet**

### Backgrounds

```html
<div class="bg-page">
  <!-- Main page background -->
  <div class="bg-card">
    <!-- Cards, panels, modals -->
    <div class="bg-elevated">
      <!-- Dropdowns, tooltips -->
      <button class="bg-button-primary">
        <!-- Primary buttons -->
        <button class="bg-button-secondary"><!-- Secondary buttons --></button>
      </button>
    </div>
  </div>
</div>
```

### Text Colors

```html
<h1 class="text-primary">
  <!-- Headings, main text -->
  <p class="text-secondary">
    <!-- Descriptions, labels -->
    <a class="text-accent">
      <!-- Links, highlights -->
      <span class="text-on-primary"
        ><!-- Text on primary buttons -->
        <span class="text-on-dark"> <!-- Text on dark surfaces --></span></span
      ></a
    >
  </p>
</h1>
```

### Icons

```html
<mat-icon class="icon-accent">
  <!-- Brand/primary icons -->
  <mat-icon class="icon-primary">
    <!-- Standard icons -->
    <mat-icon class="icon-secondary">
      <!-- Muted icons -->
      <mat-icon class="icon-on-primary">
        <!-- Icons on primary bg --></mat-icon
      ></mat-icon
    ></mat-icon
  ></mat-icon
>
```

### Borders

```html
<div class="border border-subtle">
  <!-- Light borders -->
  <div class="border border-normal"><!-- Standard borders --></div>
</div>
```

## 🏗️ **Component Patterns**

### Header Icons

```html
<button mat-icon-button class="header-icon-button">
  <mat-icon class="icon-accent">notifications</mat-icon>
</button>
```

### Cards

```html
<mat-card class="bg-card border border-subtle">
  <mat-card-header>
    <mat-card-title class="text-primary">Title</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <p class="text-secondary">Description</p>
  </mat-card-content>
</mat-card>
```

### Buttons

```html
<!-- Primary Button -->
<button mat-flat-button class="bg-button-primary text-on-primary">
  Primary Action
</button>

<!-- Secondary Button -->
<button
  mat-stroked-button
  class="bg-button-secondary text-primary border-normal">
  Secondary Action
</button>
```

### Notifications/Alerts

```html
<div class="bg-card border border-subtle">
  <div class="bg-button-primary text-on-primary">
    <!-- Icon/Initial -->
  </div>
  <div>
    <p class="text-primary">Title</p>
    <p class="text-secondary">Message</p>
  </div>
</div>
```

## 🎯 **Color Values Reference**

### Light Mode

| Purpose         | Class                          | Color     |
| --------------- | ------------------------------ | --------- |
| Page Background | `.bg-page`                     | `#f7f9ff` |
| Card Background | `.bg-card`                     | `#eceef4` |
| Primary Text    | `.text-primary`                | `#181c20` |
| Secondary Text  | `.text-secondary`              | `#43474e` |
| Brand Color     | `.text-accent`, `.icon-accent` | `#296197` |
| Subtle Border   | `.border-subtle`               | `#c3c6cf` |

### Dark Mode

| Purpose         | Class                          | Color     |
| --------------- | ------------------------------ | --------- |
| Page Background | `.bg-page`                     | `#101418` |
| Card Background | `.bg-card`                     | `#1c2024` |
| Primary Text    | `.text-primary`                | `#e0e2e8` |
| Secondary Text  | `.text-secondary`              | `#c3c6cf` |
| Brand Color     | `.text-accent`, `.icon-accent` | `#a1cafd` |
| Subtle Border   | `.border-subtle`               | `#43474e` |

## ❌ **What NOT to Use**

### Avoid These

```html
<!-- DON'T: Inline styles -->
<div style="background-color: var(--md-sys-color-surface)">
  <!-- DON'T: Technical Material Design names -->
  <div class="u-bg-surface-container-highest">
    <!-- DON'T: Hardcoded colors -->
    <div style="background: #f7f9ff">
      <!-- DON'T: Tailwind conflicting classes -->
      <div class="bg-white text-black"></div>
    </div>
  </div>
</div>
```

### Use These Instead

```html
<!-- DO: Semantic classes -->
<div class="bg-card">
  <!-- DO: Meaningful names -->
  <div class="bg-elevated">
    <!-- DO: Theme-aware classes -->
    <div class="bg-page text-primary"></div>
  </div>
</div>
```

## 🔧 **Migration Checklist**

When updating a component:

- [ ] Replace `style="background: ..."` with `.bg-*` classes
- [ ] Replace `style="color: ..."` with `.text-*` classes
- [ ] Replace hardcoded colors with semantic classes
- [ ] Test in both light and dark modes
- [ ] Ensure proper contrast ratios
- [ ] Move component-specific styles to component CSS files

## 📱 **Testing Checklist**

- [ ] Component looks correct in light mode
- [ ] Component looks correct in dark mode
- [ ] Theme toggle works smoothly
- [ ] No hardcoded colors remain
- [ ] Proper contrast for accessibility
- [ ] Consistent with design system

## 🆘 **Common Issues & Solutions**

### Issue: Colors not changing in dark mode

**Solution:** Check if using semantic classes instead of hardcoded values

### Issue: Material components not following theme

**Solution:** Verify CSS custom property overrides in `styles.scss`

### Issue: Inconsistent colors across components

**Solution:** Use semantic classes instead of direct CSS custom properties

### Issue: Poor contrast in dark mode

**Solution:** Use appropriate text classes (`.text-primary`, `.text-secondary`)

## 📞 **Need Help?**

1. Check the [full design system documentation](./design-system.md)
2. Look at existing implemented components (header, sidebar)
3. Test your changes in both light and dark modes
4. Ensure semantic class names make sense to other developers
